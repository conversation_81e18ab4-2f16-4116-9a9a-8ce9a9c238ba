import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

// Create both regular and admin clients
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export async function GET(request: NextRequest) {
  const results: any = {
    environment: {
      hasUrl: !!supabaseUrl,
      hasAnonKey: !!supabaseAnonKey,
      hasServiceKey: !!supabaseServiceKey,
      url: supabaseUrl
    },
    tests: {}
  };

  try {
    // Test 1: Basic connection with anon key
    const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);
    
    try {
      const { data: anonTest, error: anonError } = await supabaseAnon
        .from('categories')
        .select('count')
        .limit(1);
      
      results.tests.anonConnection = {
        success: !anonError,
        error: anonError?.message,
        data: anonTest
      };
    } catch (err: any) {
      results.tests.anonConnection = {
        success: false,
        error: err.message
      };
    }

    // Test 2: Admin connection with service key
    if (supabaseServiceKey) {
      const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
      
      try {
        const { data: adminTest, error: adminError } = await supabaseAdmin
          .from('categories')
          .select('*')
          .limit(5);
        
        results.tests.adminConnection = {
          success: !adminError,
          error: adminError?.message,
          data: adminTest,
          count: adminTest?.length || 0
        };
      } catch (err: any) {
        results.tests.adminConnection = {
          success: false,
          error: err.message
        };
      }

      // Test 3: Check if tables exist
      try {
        const { data: tablesTest, error: tablesError } = await supabaseAdmin
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .in('table_name', ['categories', 'forum_categories', 'profiles', 'articles']);
        
        results.tests.tablesExist = {
          success: !tablesError,
          error: tablesError?.message,
          tables: tablesTest?.map(t => t.table_name) || []
        };
      } catch (err: any) {
        results.tests.tablesExist = {
          success: false,
          error: err.message
        };
      }

      // Test 4: Check RLS policies
      try {
        const { data: rlsTest, error: rlsError } = await supabaseAdmin
          .from('pg_policies')
          .select('tablename, policyname')
          .eq('tablename', 'categories');
        
        results.tests.rlsPolicies = {
          success: !rlsError,
          error: rlsError?.message,
          policies: rlsTest || []
        };
      } catch (err: any) {
        results.tests.rlsPolicies = {
          success: false,
          error: err.message
        };
      }
    }

    return NextResponse.json(results);
  } catch (error: any) {
    return NextResponse.json({
      ...results,
      globalError: error.message
    }, { status: 500 });
  }
}
