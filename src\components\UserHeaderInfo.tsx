'use client'

import { useAuth } from './AuthProvider'
import { useRouter } from 'next/navigation'
import NotificationsDropdown from './NotificationsDropdown'
import UserDropdownMenu from './UserDropdownMenu'
import { useEffect, useState } from 'react'

export default function UserHeaderInfo() {
  const { user, supabase, loading } = useAuth()
  const router = useRouter()
  const [authTimeout, setAuthTimeout] = useState(false)

  // Set a timeout to show login/register buttons if auth takes too long
  useEffect(() => {
    const timer = setTimeout(() => {
      if (loading) {
        setAuthTimeout(true)
      }
    }, 1500) // Show login/register after 1.5 seconds if still loading

    return () => clearTimeout(timer)
  }, [loading])

  // If authenticated, show user menu
  if (user) {
    return (
      <div className="flex items-center space-x-3">
        {/* Notifications */}
        <NotificationsDropdown />

        {/* User Menu */}
        <UserDropdownMenu user={user} supabase={supabase} />
      </div>
    )
  }

  // If loading and not timed out yet, show loading indicator
  if (loading && !authTimeout) {
    return (
      <div className="flex items-center gap-2">
        <div className="animate-pulse bg-white/20 h-8 w-20 rounded"></div>
      </div>
    )
  }

  // Show login/register buttons by default or after timeout
  return (
    <div className="flex items-center gap-2 sm:gap-3">
      <a
        href="/auth/signin"
        className="bg-green-800 text-white px-2 py-1 sm:px-4 sm:py-2 rounded text-sm sm:text-base font-medium shadow-sm border border-green-900/20 hover:bg-green-700 transition-colors"
      >
        Sign In
      </a>
      <a
        href="/auth/signup"
        className="bg-yellow-600 text-white px-2 py-1 sm:px-4 sm:py-2 rounded text-sm sm:text-base font-medium shadow-sm border border-yellow-700 hover:bg-yellow-500 transition-colors"
      >
        Register
      </a>
    </div>
  )
}