'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function TestConnectionPage() {
  const [connectionStatus, setConnectionStatus] = useState('Testing...');
  const [error, setError] = useState('');
  const [envVars, setEnvVars] = useState({
    url: '',
    hasAnonKey: false,
    hasServiceKey: false
  });

  useEffect(() => {
    // Check environment variables
    setEnvVars({
      url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set',
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
    });

    // Test Supabase connection
    async function testConnection() {
      try {
        const supabase = createClientComponentClient();
        
        // Test a simple query
        const { data, error } = await supabase
          .from('categories')
          .select('count')
          .limit(1);

        if (error) {
          setError(`Supabase Error: ${error.message}`);
          setConnectionStatus('Failed');
        } else {
          setConnectionStatus('Connected successfully!');
        }
      } catch (err: any) {
        setError(`Connection Error: ${err.message}`);
        setConnectionStatus('Failed');
      }
    }

    testConnection();
  }, []);

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Supabase Connection Test</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Environment Variables</h2>
          <div className="space-y-2">
            <div>
              <strong>NEXT_PUBLIC_SUPABASE_URL:</strong> 
              <span className={envVars.url === 'Not set' ? 'text-red-600' : 'text-green-600'}>
                {envVars.url}
              </span>
            </div>
            <div>
              <strong>NEXT_PUBLIC_SUPABASE_ANON_KEY:</strong> 
              <span className={!envVars.hasAnonKey ? 'text-red-600' : 'text-green-600'}>
                {envVars.hasAnonKey ? 'Set' : 'Not set'}
              </span>
            </div>
            <div>
              <strong>SUPABASE_SERVICE_ROLE_KEY:</strong> 
              <span className={!envVars.hasServiceKey ? 'text-red-600' : 'text-green-600'}>
                {envVars.hasServiceKey ? 'Set' : 'Not set'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          <div className="space-y-2">
            <div>
              <strong>Status:</strong> 
              <span className={connectionStatus === 'Connected successfully!' ? 'text-green-600' : 'text-red-600'}>
                {connectionStatus}
              </span>
            </div>
            {error && (
              <div>
                <strong>Error:</strong> 
                <span className="text-red-600">{error}</span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">Troubleshooting Steps:</h3>
          <ol className="list-decimal list-inside text-yellow-700 space-y-1">
            <li>Make sure all environment variables are set in .env.local</li>
            <li>Restart the development server after updating .env.local</li>
            <li>Check that your Supabase project is active and accessible</li>
            <li>Verify the database has the required tables (categories, profiles, etc.)</li>
            <li>Check if RLS (Row Level Security) policies are properly configured</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
