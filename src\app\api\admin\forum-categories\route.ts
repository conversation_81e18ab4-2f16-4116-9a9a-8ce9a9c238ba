import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { name, slug, description, icon, display_order } = await request.json();

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Name and slug are required' },
        { status: 400 }
      );
    }

    console.log('Creating forum category with data:', {
      name,
      slug,
      description,
      icon,
      display_order
    });

    // Check if slug already exists
    const { data: existingCategory } = await supabaseAdmin
      .from('forum_categories')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingCategory) {
      return NextResponse.json(
        { error: `A forum category with the slug "${slug}" already exists` },
        { status: 400 }
      );
    }

    // Create the forum category using the admin client (bypasses RLS)
    const { data: category, error } = await supabaseAdmin
      .from('forum_categories')
      .insert({
        name,
        slug,
        description,
        icon,
        display_order: display_order || 0
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating forum category:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    console.log('Forum category created successfully:', category);

    return NextResponse.json(category);
  } catch (error: any) {
    console.error('Unexpected error in forum categories API:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get all forum categories
    const { data: categories, error } = await supabaseAdmin
      .from('forum_categories')
      .select('*')
      .order('display_order', { ascending: true });

    if (error) {
      console.error('Error fetching forum categories:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(categories);
  } catch (error: any) {
    console.error('Unexpected error in forum categories API:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Clear all forum categories
    const { error } = await supabaseAdmin
      .from('forum_categories')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all except a dummy ID

    if (error) {
      console.error('Error clearing forum categories:', error);
      return NextResponse.json(
        { error: 'Failed to clear forum categories: ' + error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      message: 'All forum categories cleared successfully' 
    });

  } catch (error: any) {
    console.error('Unexpected error in clear forum categories API:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
