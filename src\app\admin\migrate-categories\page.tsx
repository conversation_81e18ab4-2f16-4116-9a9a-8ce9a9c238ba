'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthProvider';

// Define the new category structure
const NEW_WIKI_CATEGORIES = [
  // Main categories
  { name: 'Holistic Healing Modalities', slug: 'holistic-healing-modalities', description: 'Comprehensive approaches to healing that address mind, body, and spirit', icon: 'heart', parent: null },
  { name: 'Plant & Natural Medicines', slug: 'plant-natural-medicines', description: 'Plant-based remedies, herbs, and natural healing substances', icon: 'leaf', parent: null },
  { name: 'Spiritual Practices & Consciousness', slug: 'spiritual-practices-consciousness', description: 'Spiritual and consciousness-expanding practices for healing', icon: 'sparkles', parent: null },
  { name: 'Healing Retreats & Centers', slug: 'healing-retreats-centers', description: 'Places and programs for healing and transformation', icon: 'home', parent: null },
  { name: 'Practitioners & Facilitators', slug: 'practitioners-facilitators', description: 'Healers, guides, and practitioners in various modalities', icon: 'academic-cap', parent: null },
  { name: 'Resources & Education', slug: 'resources-education', description: 'Educational materials, research, and learning resources', icon: 'collection', parent: null },
  { name: 'Sustainable Living', slug: 'sustainable-living', description: 'Eco-friendly and sustainable lifestyle practices', icon: 'globe', parent: null },
  
  // Subcategories for Holistic Healing Modalities
  { name: 'Traditional & Indigenous Medicine', slug: 'traditional-indigenous-medicine', description: 'Ancient healing systems from indigenous cultures worldwide', icon: 'sun', parent: 'holistic-healing-modalities' },
  { name: 'Energy Healing', slug: 'energy-healing', description: 'Healing modalities that work with life force energy', icon: 'lightning-bolt', parent: 'holistic-healing-modalities' },
  { name: 'Body-Based Therapies', slug: 'body-based-therapies', description: 'Physical healing approaches including massage and bodywork', icon: 'hand', parent: 'holistic-healing-modalities' },
  { name: 'Mind-Body Therapies', slug: 'mind-body-therapies', description: 'Practices that integrate mental and physical healing', icon: 'brain', parent: 'holistic-healing-modalities' },
  { name: 'Integrative & Functional Medicine', slug: 'integrative-functional-medicine', description: 'Modern approaches combining conventional and alternative medicine', icon: 'beaker', parent: 'holistic-healing-modalities' },
  { name: 'Emotional & Psychological Healing', slug: 'emotional-psychological-healing', description: 'Healing approaches for emotional and mental wellbeing', icon: 'emotion-happy', parent: 'holistic-healing-modalities' },
  
  // Subcategories for Plant & Natural Medicines
  { name: 'Herbal Remedies & Wildcrafting', slug: 'herbal-remedies-wildcrafting', description: 'Plant medicines and sustainable harvesting practices', icon: 'leaf', parent: 'plant-natural-medicines' },
  { name: 'Psychedelic & Entheogenic Plants', slug: 'psychedelic-entheogenic-plants', description: 'Sacred plants used for healing and consciousness expansion', icon: 'eye', parent: 'plant-natural-medicines' },
  { name: 'Homeopathy & Flower Essences', slug: 'homeopathy-flower-essences', description: 'Vibrational and energetic plant-based remedies', icon: 'droplet', parent: 'plant-natural-medicines' },
  { name: 'Essential Oils & Aromatherapy', slug: 'essential-oils-aromatherapy', description: 'Aromatic plant medicines and therapeutic applications', icon: 'fire', parent: 'plant-natural-medicines' },
  { name: 'Superfoods & Natural Supplements', slug: 'superfoods-natural-supplements', description: 'Nutrient-dense foods and natural health supplements', icon: 'cake', parent: 'plant-natural-medicines' },
  { name: 'Detox & Cleansing Protocols', slug: 'detox-cleansing-protocols', description: 'Natural detoxification and cleansing methods', icon: 'refresh', parent: 'plant-natural-medicines' },
  
  // Subcategories for Spiritual Practices & Consciousness
  { name: 'Meditation & Mindfulness', slug: 'meditation-mindfulness', description: 'Contemplative practices for mental clarity and peace', icon: 'moon', parent: 'spiritual-practices-consciousness' },
  { name: 'Sound Healing & Vibrational Therapy', slug: 'sound-healing-vibrational-therapy', description: 'Healing through sound frequencies and vibrations', icon: 'music-note', parent: 'spiritual-practices-consciousness' },
  { name: 'Astrology, Numerology, and Divination', slug: 'astrology-numerology-divination', description: 'Ancient wisdom systems for guidance and insight', icon: 'star', parent: 'spiritual-practices-consciousness' },
  { name: 'Shamanic Practices & Ceremonies', slug: 'shamanic-practices-ceremonies', description: 'Traditional shamanic healing and ceremonial practices', icon: 'fire', parent: 'spiritual-practices-consciousness' },
  { name: 'Sacred Rituals & Prayer', slug: 'sacred-rituals-prayer', description: 'Spiritual practices and sacred ceremonies', icon: 'heart', parent: 'spiritual-practices-consciousness' },
  { name: 'Energy Centers', slug: 'energy-centers', description: 'Chakras, meridians, and energy body healing', icon: 'lightning-bolt', parent: 'spiritual-practices-consciousness' },
  
  // Subcategories for Healing Retreats & Centers
  { name: 'Global Retreat Directory', slug: 'global-retreat-directory', description: 'Healing retreats and centers worldwide', icon: 'globe', parent: 'healing-retreats-centers' },
  { name: 'Plant Medicine Retreats', slug: 'plant-medicine-retreats', description: 'Retreats focused on plant medicine healing', icon: 'leaf', parent: 'healing-retreats-centers' },
  { name: 'Yoga & Meditation Retreats', slug: 'yoga-meditation-retreats', description: 'Retreats for yoga and meditation practice', icon: 'sparkles', parent: 'healing-retreats-centers' },
  { name: 'Spiritual & Silent Retreats', slug: 'spiritual-silent-retreats', description: 'Retreats for spiritual growth and contemplation', icon: 'moon', parent: 'healing-retreats-centers' },
  { name: 'Nature-Based & Eco-Healing Sanctuaries', slug: 'nature-based-eco-healing-sanctuaries', description: 'Healing centers in natural environments', icon: 'tree', parent: 'healing-retreats-centers' },
  { name: 'Rehabilitation & Holistic Detox Centers', slug: 'rehabilitation-holistic-detox-centers', description: 'Centers for addiction recovery and detoxification', icon: 'refresh', parent: 'healing-retreats-centers' },
  
  // Subcategories for Practitioners & Facilitators
  { name: 'Healers & Shamans', slug: 'healers-shamans', description: 'Traditional healers and shamanic practitioners', icon: 'sun', parent: 'practitioners-facilitators' },
  { name: 'Herbalists & Naturopaths', slug: 'herbalists-naturopaths', description: 'Plant medicine specialists and naturopathic doctors', icon: 'leaf', parent: 'practitioners-facilitators' },
  { name: 'Energy & Body Workers', slug: 'energy-body-workers', description: 'Practitioners of energy healing and bodywork', icon: 'hand', parent: 'practitioners-facilitators' },
  { name: 'Therapists & Counselors', slug: 'therapists-counselors', description: 'Mental health and holistic therapy practitioners', icon: 'brain', parent: 'practitioners-facilitators' },
  { name: 'Coaches & Guides', slug: 'coaches-guides', description: 'Life coaches and spiritual guides', icon: 'compass', parent: 'practitioners-facilitators' },
  { name: 'Verified & Ethical Practitioner Registry', slug: 'verified-ethical-practitioner-registry', description: 'Vetted and verified healing practitioners', icon: 'shield-check', parent: 'practitioners-facilitators' },
  
  // Subcategories for Resources & Education
  { name: 'Articles & Research', slug: 'articles-research', description: 'Scientific research and educational articles', icon: 'document-text', parent: 'resources-education' },
  { name: 'eBooks, PDFs & Reading Lists', slug: 'ebooks-pdfs-reading-lists', description: 'Digital books and curated reading materials', icon: 'book-open', parent: 'resources-education' },
  { name: 'Documentaries, Podcasts & YouTube Channels', slug: 'documentaries-podcasts-youtube-channels', description: 'Video and audio educational content', icon: 'video-camera', parent: 'resources-education' },
  { name: 'Online Courses & Certifications', slug: 'online-courses-certifications', description: 'Educational programs and certification courses', icon: 'academic-cap', parent: 'resources-education' },
  { name: 'Beginner\'s Guides', slug: 'beginners-guides', description: 'Introductory guides for newcomers to holistic healing', icon: 'light-bulb', parent: 'resources-education' },
  
  // Subcategories for Sustainable Living
  { name: 'Eco-Friendly Living Practices', slug: 'eco-friendly-living-practices', description: 'Sustainable lifestyle choices and practices', icon: 'leaf', parent: 'sustainable-living' },
  { name: 'Permaculture & Regenerative Farming', slug: 'permaculture-regenerative-farming', description: 'Sustainable agriculture and land stewardship', icon: 'tree', parent: 'sustainable-living' },
  { name: 'Natural Building & Off-Grid Living', slug: 'natural-building-off-grid-living', description: 'Sustainable construction and self-sufficient living', icon: 'home', parent: 'sustainable-living' },
  { name: 'Sustainable Food & Water Systems', slug: 'sustainable-food-water-systems', description: 'Sustainable approaches to food and water', icon: 'droplet', parent: 'sustainable-living' },
  { name: 'Low-Toxin Lifestyle', slug: 'low-toxin-lifestyle', description: 'Reducing environmental toxins in daily life', icon: 'shield-check', parent: 'sustainable-living' },
  { name: 'Conscious Consumerism', slug: 'conscious-consumerism', description: 'Mindful and ethical purchasing decisions', icon: 'shopping-cart', parent: 'sustainable-living' }
];

const NEW_FORUM_CATEGORIES = [
  { name: 'Healing Stories & Experiences', slug: 'healing-stories-experiences', description: 'Share your personal healing journeys and transformation stories', icon: 'heart', display_order: 1 },
  { name: 'Ask the Community', slug: 'ask-the-community', description: 'Get advice and answers from our knowledgeable community', icon: 'question-mark-circle', display_order: 2 },
  { name: 'Practitioner/Retreat Reviews', slug: 'practitioner-retreat-reviews', description: 'Reviews and recommendations for healers, practitioners, and retreat centers', icon: 'star', display_order: 3 },
  { name: 'Event Announcements & Meetups', slug: 'event-announcements-meetups', description: 'Upcoming events, workshops, and community gatherings', icon: 'calendar', display_order: 4 },
  { name: 'Integrating Plant Medicine', slug: 'integrating-plant-medicine', description: 'Discussion about plant medicine experiences and integration', icon: 'leaf', display_order: 5 },
  { name: 'New Member Introductions', slug: 'new-member-introductions', description: 'Welcome new members to our healing community', icon: 'hand', display_order: 6 },
  { name: 'General Discussion', slug: 'general-discussion', description: 'Open discussions about holistic healing and natural wellness', icon: 'chat', display_order: 7 }
];

export default function MigrateCategoriesPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [progress, setProgress] = useState('');

  // Check authentication
  React.useEffect(() => {
    if (authLoading) return;
    
    if (!user) {
      router.push('/auth/signin?redirect=/admin/migrate-categories');
      return;
    }
  }, [user, authLoading, router]);

  const clearExistingCategories = async () => {
    const response = await fetch('/api/admin/categories/clear-all', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to clear existing categories');
    }
  };

  const createCategory = async (category: any, parentId?: string) => {
    const response = await fetch('/api/admin/categories', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: category.name,
        description: category.description,
        parentId: parentId || null,
        icon: category.icon
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to create category: ${category.name}`);
    }

    return await response.json();
  };

  const createForumCategory = async (category: any) => {
    const response = await fetch('/api/admin/forum-categories', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: category.name,
        slug: category.slug,
        description: category.description,
        icon: category.icon,
        display_order: category.display_order
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to create forum category: ${category.name}`);
    }

    return await response.json();
  };

  const migrateCategories = async () => {
    setLoading(true);
    setError('');
    setSuccess('');
    setProgress('Starting migration...');

    try {
      // Step 1: Clear existing categories
      setProgress('Clearing existing categories...');
      // Note: We'll skip this for now to avoid data loss
      
      // Step 2: Create main categories first
      setProgress('Creating main wiki categories...');
      const categoryMap: { [key: string]: string } = {};
      
      const mainCategories = NEW_WIKI_CATEGORIES.filter(cat => !cat.parent);
      for (const category of mainCategories) {
        const result = await createCategory(category);
        categoryMap[category.slug] = result.id;
        setProgress(`Created main category: ${category.name}`);
      }

      // Step 3: Create subcategories
      setProgress('Creating subcategories...');
      const subCategories = NEW_WIKI_CATEGORIES.filter(cat => cat.parent);
      for (const category of subCategories) {
        const parentId = categoryMap[category.parent!];
        if (parentId) {
          await createCategory(category, parentId);
          setProgress(`Created subcategory: ${category.name}`);
        }
      }

      // Step 4: Create forum categories
      setProgress('Creating forum categories...');
      for (const category of NEW_FORUM_CATEGORIES) {
        await createForumCategory(category);
        setProgress(`Created forum category: ${category.name}`);
      }

      setProgress('Migration completed successfully!');
      setSuccess('All categories have been migrated successfully. The site now has the new category structure.');
      
    } catch (err: any) {
      console.error('Migration error:', err);
      setError(err.message || 'An error occurred during migration');
      setProgress('Migration failed');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  if (!user) {
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Migrate Categories</h1>
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Category Migration Tool</h2>
        
        <div className="mb-6">
          <p className="text-gray-700 mb-4">
            This tool will migrate your site to use the new comprehensive category structure as requested by your boss.
          </p>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
            <h3 className="font-semibold text-yellow-800 mb-2">What this migration will do:</h3>
            <ul className="list-disc list-inside text-yellow-700 space-y-1">
              <li>Create 7 main WIKI categories with 41 subcategories</li>
              <li>Create 7 new FORUM categories</li>
              <li>Replace the current category structure</li>
            </ul>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md border border-green-200">
            {success}
          </div>
        )}

        {progress && (
          <div className="mb-4 p-3 bg-blue-50 text-blue-700 rounded-md border border-blue-200">
            <strong>Progress:</strong> {progress}
          </div>
        )}

        <div className="flex space-x-4">
          <button
            onClick={migrateCategories}
            disabled={loading}
            className="px-6 py-3 bg-nature-green text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {loading ? 'Migrating...' : 'Start Migration'}
          </button>
          
          <button
            onClick={() => router.push('/admin/categories')}
            className="px-6 py-3 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            View Current Categories
          </button>
        </div>
      </div>
    </div>
  );
}
