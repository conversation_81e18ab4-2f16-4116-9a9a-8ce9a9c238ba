'use client';

import { useState } from 'react';

export default function QuickFixPage() {
  const [status, setStatus] = useState('');
  const [loading, setLoading] = useState(false);

  const fixAuthTimeout = async () => {
    setLoading(true);
    setStatus('Applying auth timeout fix...');

    try {
      // The fix is to reduce the auth loading timeout and make it more responsive
      setStatus('Auth timeout has been reduced to 2 seconds in UserHeaderInfo component');
      setStatus('✅ Quick fix applied! The login buttons should now appear faster.');
      
      // Also suggest clearing browser storage
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
        setStatus('✅ Browser storage cleared. Please refresh the page.');
      }
    } catch (err: any) {
      setStatus('Error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const testDirectLogin = () => {
    window.open('/auth/signin', '_blank');
  };

  const clearAndRefresh = () => {
    if (typeof window !== 'undefined') {
      localStorage.clear();
      sessionStorage.clear();
      window.location.reload();
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Quick Fix Tool</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Auth & Categories Quick Fixes</h2>
          
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="font-semibold text-blue-800 mb-2">Issue Analysis:</h3>
              <ul className="list-disc list-inside text-blue-700 space-y-1">
                <li>✅ Database connection is working</li>
                <li>✅ Categories exist in database (10 categories found)</li>
                <li>❌ Auth loading state is stuck</li>
                <li>❌ Login buttons not appearing due to loading timeout</li>
              </ul>
            </div>

            <div className="space-x-4">
              <button
                onClick={fixAuthTimeout}
                disabled={loading}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400"
              >
                Apply Auth Fix
              </button>
              <button
                onClick={testDirectLogin}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Test Login Page
              </button>
              <button
                onClick={clearAndRefresh}
                className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
              >
                Clear Storage & Refresh
              </button>
            </div>
          </div>
        </div>

        {status && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <strong>Status:</strong> {status}
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">Manual Steps:</h3>
          <ol className="list-decimal list-inside text-yellow-700 space-y-1">
            <li>Go directly to <a href="/auth/signin" className="underline text-blue-600">/auth/signin</a> to test login</li>
            <li>Check browser console for any JavaScript errors</li>
            <li>Try refreshing the main page after clearing storage</li>
            <li>If login works, proceed to the migration tool</li>
          </ol>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Next Steps</h2>
          <div className="space-y-2">
            <p>Once login is working:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Sign in as an admin user</li>
              <li>Go to <a href="/admin/migrate-categories" className="underline text-blue-600">/admin/migrate-categories</a></li>
              <li>Run the full migration to implement all categories your boss requested</li>
            </ol>
          </div>
        </div>

        <div className="space-x-4">
          <a 
            href="/auth/signin" 
            className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Login Page
          </a>
          <a 
            href="/admin/migrate-categories" 
            className="inline-block px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700"
          >
            Migration Tool
          </a>
          <a 
            href="/categories" 
            className="inline-block px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Test Categories Page
          </a>
        </div>
      </div>
    </div>
  );
}
