'use client';

import { useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export default function DebugFixPage() {
  const [status, setStatus] = useState('');
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const supabase = createClientComponentClient();

  const runDiagnostics = async () => {
    setLoading(true);
    setStatus('Running diagnostics...');
    const diagnostics: any = {};

    try {
      // Test 1: Check if categories table exists and has data
      setStatus('Checking categories table...');
      const { data: categories, error: catError } = await supabase
        .from('categories')
        .select('*')
        .limit(10);

      diagnostics.categories = {
        error: catError?.message,
        count: categories?.length || 0,
        data: categories
      };

      // Test 2: Check if we can create a category (tests RLS)
      setStatus('Testing category creation...');
      const { data: testCat, error: createError } = await supabase
        .from('categories')
        .insert({
          name: 'Test Category',
          slug: 'test-category-' + Date.now(),
          description: 'Test category for debugging'
        })
        .select()
        .single();

      diagnostics.categoryCreation = {
        error: createError?.message,
        success: !createError,
        data: testCat
      };

      // Test 3: Check auth
      setStatus('Checking authentication...');
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      diagnostics.auth = {
        error: authError?.message,
        hasUser: !!user,
        user: user ? { id: user.id, email: user.email } : null
      };

      // Test 4: Check profiles table
      setStatus('Checking profiles table...');
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .limit(5);

      diagnostics.profiles = {
        error: profileError?.message,
        count: profiles?.length || 0
      };

      setResults(diagnostics);
      setStatus('Diagnostics complete');

    } catch (err: any) {
      setStatus('Error: ' + err.message);
      setResults({ error: err.message });
    } finally {
      setLoading(false);
    }
  };

  const fixBasicIssues = async () => {
    setLoading(true);
    setStatus('Fixing basic issues...');

    try {
      // Create some basic categories using the admin API
      setStatus('Creating basic categories...');
      
      const basicCategories = [
        { name: 'Herbal Remedies', slug: 'herbal-remedies', description: 'Natural plant-based medicines', icon: 'leaf' },
        { name: 'Mind-Body Practices', slug: 'mind-body-practices', description: 'Yoga, meditation, and mindfulness', icon: 'sparkles' },
        { name: 'Traditional Medicine', slug: 'traditional-medicine', description: 'Ancient healing systems', icon: 'sun' }
      ];

      for (const category of basicCategories) {
        const response = await fetch('/api/admin/categories', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(category)
        });
        
        if (response.ok) {
          setStatus(`Created category: ${category.name}`);
        } else {
          const error = await response.json();
          setStatus(`Failed to create ${category.name}: ${error.error}`);
        }
      }

      setStatus('Basic categories created. Refreshing diagnostics...');
      await runDiagnostics();

    } catch (err: any) {
      setStatus('Error fixing issues: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const testAuth = async () => {
    setLoading(true);
    setStatus('Testing authentication...');

    try {
      // Try to sign up a test user
      const { data, error } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'testpassword123'
      });

      if (error) {
        setStatus('Auth test failed: ' + error.message);
      } else {
        setStatus('Auth test successful - check your email for confirmation');
      }
    } catch (err: any) {
      setStatus('Auth error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Debug & Fix Tool</h1>
      
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="space-x-4">
            <button
              onClick={runDiagnostics}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
            >
              Run Diagnostics
            </button>
            <button
              onClick={fixBasicIssues}
              disabled={loading}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400"
            >
              Fix Basic Issues
            </button>
            <button
              onClick={testAuth}
              disabled={loading}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-400"
            >
              Test Auth
            </button>
          </div>
        </div>

        {status && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <strong>Status:</strong> {status}
          </div>
        )}

        {Object.keys(results).length > 0 && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Diagnostic Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">Common Issues & Solutions:</h3>
          <ul className="list-disc list-inside text-yellow-700 space-y-1">
            <li><strong>Categories not loading:</strong> Usually means empty table or RLS blocking access</li>
            <li><strong>No login page:</strong> Check if auth is properly configured in Supabase</li>
            <li><strong>RLS errors:</strong> May need to disable RLS temporarily or fix policies</li>
            <li><strong>Empty database:</strong> Run the migration tool to populate categories</li>
          </ul>
        </div>

        <div className="space-x-4">
          <a 
            href="/admin/migrate-categories" 
            className="inline-block px-4 py-2 bg-nature-green text-white rounded hover:bg-green-700"
          >
            Go to Migration Tool
          </a>
          <a 
            href="/test-connection" 
            className="inline-block px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Test Connection
          </a>
          <a 
            href="/categories" 
            className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            View Categories Page
          </a>
        </div>
      </div>
    </div>
  );
}
