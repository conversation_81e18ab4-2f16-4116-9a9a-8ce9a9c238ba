import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Check if the service role key is set
    const hasServiceKey = !!process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    return NextResponse.json({ 
      hasServiceKey,
      message: hasServiceKey ? 'Service role key is set' : 'Service role key is not set'
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
