-- Update Categories Structure for NatureHeals.info
-- This migration replaces the existing categories with the new comprehensive structure

-- Clear existing categories first (this will cascade to articles, but we'll handle that)
DELETE FROM public.categories;

-- Insert main WIKI categories first
INSERT INTO public.categories (id, name, slug, description, icon) VALUES
('11111111-1111-1111-1111-111111111111', 'Holistic Healing Modalities', 'holistic-healing-modalities', 'Comprehensive approaches to healing that address mind, body, and spirit', 'heart'),
('22222222-2222-2222-2222-222222222222', 'Plant & Natural Medicines', 'plant-natural-medicines', 'Plant-based remedies, herbs, and natural healing substances', 'leaf'),
('*************-3333-3333-************', 'Spiritual Practices & Consciousness', 'spiritual-practices-consciousness', 'Spiritual and consciousness-expanding practices for healing', 'sparkles'),
('*************-4444-4444-************', 'Healing Retreats & Centers', 'healing-retreats-centers', 'Places and programs for healing and transformation', 'home'),
('*************-5555-5555-************', 'Practitioners & Facilitators', 'practitioners-facilitators', 'Healers, guides, and practitioners in various modalities', 'academic-cap'),
('*************-6666-6666-************', 'Resources & Education', 'resources-education', 'Educational materials, research, and learning resources', 'collection'),
('*************-7777-7777-************', 'Sustainable Living', 'sustainable-living', 'Eco-friendly and sustainable lifestyle practices', 'globe');

-- Insert subcategories for Holistic Healing Modalities
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Traditional & Indigenous Medicine', 'traditional-indigenous-medicine', 'Ancient healing systems from indigenous cultures worldwide', '11111111-1111-1111-1111-111111111111', 'sun'),
('Energy Healing', 'energy-healing', 'Healing modalities that work with life force energy', '11111111-1111-1111-1111-111111111111', 'lightning-bolt'),
('Body-Based Therapies', 'body-based-therapies', 'Physical healing approaches including massage and bodywork', '11111111-1111-1111-1111-111111111111', 'hand'),
('Mind-Body Therapies', 'mind-body-therapies', 'Practices that integrate mental and physical healing', '11111111-1111-1111-1111-111111111111', 'brain'),
('Integrative & Functional Medicine', 'integrative-functional-medicine', 'Modern approaches combining conventional and alternative medicine', '11111111-1111-1111-1111-111111111111', 'beaker'),
('Emotional & Psychological Healing', 'emotional-psychological-healing', 'Healing approaches for emotional and mental wellbeing', '11111111-1111-1111-1111-111111111111', 'emotion-happy');

-- Insert subcategories for Plant & Natural Medicines
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Herbal Remedies & Wildcrafting', 'herbal-remedies-wildcrafting', 'Plant medicines and sustainable harvesting practices', '22222222-2222-2222-2222-222222222222', 'leaf'),
('Psychedelic & Entheogenic Plants', 'psychedelic-entheogenic-plants', 'Sacred plants used for healing and consciousness expansion', '22222222-2222-2222-2222-222222222222', 'eye'),
('Homeopathy & Flower Essences', 'homeopathy-flower-essences', 'Vibrational and energetic plant-based remedies', '22222222-2222-2222-2222-222222222222', 'droplet'),
('Essential Oils & Aromatherapy', 'essential-oils-aromatherapy', 'Aromatic plant medicines and therapeutic applications', '22222222-2222-2222-2222-222222222222', 'fire'),
('Superfoods & Natural Supplements', 'superfoods-natural-supplements', 'Nutrient-dense foods and natural health supplements', '22222222-2222-2222-2222-222222222222', 'cake'),
('Detox & Cleansing Protocols', 'detox-cleansing-protocols', 'Natural detoxification and cleansing methods', '22222222-2222-2222-2222-222222222222', 'refresh');

-- Insert subcategories for Spiritual Practices & Consciousness
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Meditation & Mindfulness', 'meditation-mindfulness', 'Contemplative practices for mental clarity and peace', '*************-3333-3333-************', 'moon'),
('Sound Healing & Vibrational Therapy', 'sound-healing-vibrational-therapy', 'Healing through sound frequencies and vibrations', '*************-3333-3333-************', 'music-note'),
('Astrology, Numerology, and Divination', 'astrology-numerology-divination', 'Ancient wisdom systems for guidance and insight', '*************-3333-3333-************', 'star'),
('Shamanic Practices & Ceremonies', 'shamanic-practices-ceremonies', 'Traditional shamanic healing and ceremonial practices', '*************-3333-3333-************', 'fire'),
('Sacred Rituals & Prayer', 'sacred-rituals-prayer', 'Spiritual practices and sacred ceremonies', '*************-3333-3333-************', 'heart'),
('Energy Centers', 'energy-centers', 'Chakras, meridians, and energy body healing', '*************-3333-3333-************', 'lightning-bolt');

-- Insert subcategories for Healing Retreats & Centers
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Global Retreat Directory', 'global-retreat-directory', 'Healing retreats and centers worldwide', '*************-4444-4444-************', 'globe'),
('Plant Medicine Retreats', 'plant-medicine-retreats', 'Retreats focused on plant medicine healing', '*************-4444-4444-************', 'leaf'),
('Yoga & Meditation Retreats', 'yoga-meditation-retreats', 'Retreats for yoga and meditation practice', '*************-4444-4444-************', 'sparkles'),
('Spiritual & Silent Retreats', 'spiritual-silent-retreats', 'Retreats for spiritual growth and contemplation', '*************-4444-4444-************', 'moon'),
('Nature-Based & Eco-Healing Sanctuaries', 'nature-based-eco-healing-sanctuaries', 'Healing centers in natural environments', '*************-4444-4444-************', 'tree'),
('Rehabilitation & Holistic Detox Centers', 'rehabilitation-holistic-detox-centers', 'Centers for addiction recovery and detoxification', '*************-4444-4444-************', 'refresh');

-- Insert subcategories for Practitioners & Facilitators
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Healers & Shamans', 'healers-shamans', 'Traditional healers and shamanic practitioners', '*************-5555-5555-************', 'sun'),
('Herbalists & Naturopaths', 'herbalists-naturopaths', 'Plant medicine specialists and naturopathic doctors', '*************-5555-5555-************', 'leaf'),
('Energy & Body Workers', 'energy-body-workers', 'Practitioners of energy healing and bodywork', '*************-5555-5555-************', 'hand'),
('Therapists & Counselors', 'therapists-counselors', 'Mental health and holistic therapy practitioners', '*************-5555-5555-************', 'brain'),
('Coaches & Guides', 'coaches-guides', 'Life coaches and spiritual guides', '*************-5555-5555-************', 'compass'),
('Verified & Ethical Practitioner Registry', 'verified-ethical-practitioner-registry', 'Vetted and verified healing practitioners', '*************-5555-5555-************', 'shield-check');

-- Insert subcategories for Resources & Education
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Articles & Research', 'articles-research', 'Scientific research and educational articles', '*************-6666-6666-************', 'document-text'),
('eBooks, PDFs & Reading Lists', 'ebooks-pdfs-reading-lists', 'Digital books and curated reading materials', '*************-6666-6666-************', 'book-open'),
('Documentaries, Podcasts & YouTube Channels', 'documentaries-podcasts-youtube-channels', 'Video and audio educational content', '*************-6666-6666-************', 'video-camera'),
('Online Courses & Certifications', 'online-courses-certifications', 'Educational programs and certification courses', '*************-6666-6666-************', 'academic-cap'),
('Beginner''s Guides', 'beginners-guides', 'Introductory guides for newcomers to holistic healing', '*************-6666-6666-************', 'light-bulb');

-- Insert subcategories for Sustainable Living
INSERT INTO public.categories (name, slug, description, parent_id, icon) VALUES
('Eco-Friendly Living Practices', 'eco-friendly-living-practices', 'Sustainable lifestyle choices and practices', '*************-7777-7777-************', 'leaf'),
('Permaculture & Regenerative Farming', 'permaculture-regenerative-farming', 'Sustainable agriculture and land stewardship', '*************-7777-7777-************', 'tree'),
('Natural Building & Off-Grid Living', 'natural-building-off-grid-living', 'Sustainable construction and self-sufficient living', '*************-7777-7777-************', 'home'),
('Sustainable Food & Water Systems', 'sustainable-food-water-systems', 'Sustainable approaches to food and water', '*************-7777-7777-************', 'droplet'),
('Low-Toxin Lifestyle', 'low-toxin-lifestyle', 'Reducing environmental toxins in daily life', '*************-7777-7777-************', 'shield-check'),
('Conscious Consumerism', 'conscious-consumerism', 'Mindful and ethical purchasing decisions', '*************-7777-7777-************', 'shopping-cart');
